# LORE-TSR 迁移项目 - 迭代9步骤9.2编码计划

**文档版本**: v1.0  
**创建日期**: 2025-07-20  
**当前迭代**: 迭代9 - 可视化功能扩展  
**当前步骤**: 步骤9.2 - 实现LORE-TSR图像处理工具  
**优先级**: P2（可选）  

## 📋 动态迁移蓝图更新

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 迭代1 | **复杂** | **已完成** |
| `main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 迭代1,3 | **复杂** | **已完成** |
| `lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 迭代2 | **复杂** | **已完成** |
| `lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 迭代4 | 简单 | **已完成** |
| `lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 迭代6 | **复杂** | **已完成** |
| `lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 迭代6 | **复杂** | **已完成** |
| `lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 迭代2 | 简单 | **已完成** |
| `lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 迭代5 | **复杂** | **已完成** |
| `lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 迭代11 | 简单 | 未开始 |
| `lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 迭代7 | 简单 | **已完成** |
| `lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离 | 迭代7 | 简单 | **已完成** |
| **权重处理工具** | `modules/utils/lore_tsr/weight_utils.py` | **新建** | **迭代8** | **简单** | **已完成** |
| **权重转换器** | `modules/utils/lore_tsr/weight_converter.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重加载器** | `modules/utils/lore_tsr/weight_loader.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重验证器** | `modules/utils/lore_tsr/weight_validator.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重转换脚本** | `cmd_scripts/train_table_structure/convert_lore_weights.py` | **新建** | **迭代8** | **简单** | **已完成** |
| `lib/utils/debugger.py` | `modules/visualization/lore_tsr_visualizer.py` | 重构适配 | **迭代9** | **复杂** | **✅ 基础框架完成** |
| `lib/utils/debugger.py::add_4ps_coco_bbox` | `modules/visualization/lore_tsr_image_utils.py` | 复制保留 | **迭代9** | **复杂** | **进行中** |
| `lib/utils/debugger.py::add_corner` | `modules/visualization/lore_tsr_image_utils.py` | 复制保留 | **迭代9** | **复杂** | **进行中** |
| **可视化辅助工具** | `modules/utils/lore_tsr/visualization_utils.py` | **新建** | **迭代9** | **复杂** | **未开始** |

### 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                         # [已存在，已扩展]
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                            # [已存在]
├── networks/lore_tsr/                               # [已存在]
│   ├── __init__.py                                  # [已存在]
│   ├── lore_tsr_model.py                            # [已存在]
│   ├── lore_tsr_loss.py                             # [已存在]
│   ├── processor.py                                 # [已存在]
│   ├── transformer.py                               # [已存在]
│   ├── backbones/                                   # [已存在]
│   │   ├── __init__.py                              # [已存在]
│   │   ├── fpn_resnet_half.py                       # [已存在]
│   │   ├── fpn_resnet.py                            # [已存在]
│   │   ├── fpn_mask_resnet_half.py                  # [已存在]
│   │   ├── fpn_mask_resnet.py                       # [已存在]
│   │   └── pose_dla_dcn.py                          # [已存在]
│   └── heads/                                       # [已存在]
│       ├── __init__.py                              # [已存在]
│       └── lore_tsr_head.py                         # [已存在]
├── my_datasets/table_structure_recognition/         # [已存在]
│   ├── lore_tsr_dataset.py                          # [已存在]
│   ├── lore_tsr_transforms.py                       # [已存在]
│   └── lore_tsr_target_preparation.py               # [已存在]
├── modules/utils/lore_tsr/                          # [已存在]
│   ├── __init__.py                                  # [已存在]
│   ├── weight_utils.py                              # [已存在]
│   ├── weight_converter.py                          # [已存在]
│   ├── weight_loader.py                             # [已存在]
│   ├── weight_validator.py                          # [已存在]
│   ├── visualization_utils.py                       # [待创建]
│   ├── post_process.py                              # [待创建]
│   ├── oracle_utils.py                              # [待创建]
│   └── eval_utils.py                                # [待创建]
├── modules/visualization/                           # [已存在]
│   ├── lore_tsr_visualizer.py                       # [已存在，步骤9.1完成]
│   ├── lore_tsr_image_utils.py                      # [待创建，步骤9.2]
│   ├── image_utils.py                               # [已存在]
│   ├── table_structure_visualizer.py               # [已存在，参考]
│   └── table_structure_visualizer_ms.py            # [已存在，参考]
├── cmd_scripts/train_table_structure/               # [已存在]
│   ├── convert_lore_weights.py                      # [已存在]
│   └── lore_tsr_train.sh                            # [待创建]
└── external/lore_tsr/                               # [已存在]
    ├── DCNv2/                                       # [已存在]
    ├── NMS/                                         # [已存在]
    └── cocoapi/                                     # [已存在]
```

## 🎯 步骤9.2详细计划

### 步骤标题
**迭代9步骤9.2: 实现LORE-TSR图像处理工具**

### 当前迭代
**迭代9 - 可视化功能扩展**

### 影响文件
1. `modules/visualization/lore_tsr_image_utils.py` - **新建**，LORE-TSR专用图像处理工具
2. `modules/visualization/__init__.py` - **修改**，添加新模块导入

### 具体操作

#### 1. 创建LORE-TSR图像处理工具
创建 `modules/visualization/lore_tsr_image_utils.py`，实现LORE-TSR特有的图像绘制功能：

```python
#!/usr/bin/env python3
"""
LORE-TSR专用图像处理工具
实现LORE-TSR特有的可视化功能，包括四点边界框绘制、逻辑坐标标注、角点箭头绘制等
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)

class LoreTsrImageUtils:
    """LORE-TSR专用图像处理工具类"""
    
    @staticmethod
    def load_and_preprocess_image(image_path: str) -> Tuple[Image.Image, np.ndarray, Dict[str, Any]]:
        """
        加载并预处理图像
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Tuple[Image.Image, np.ndarray, Dict]: (原始图像, 预处理张量, 元信息)
        """
        try:
            # 加载原始图像
            original_image = Image.open(image_path).convert('RGB')
            
            # 转换为numpy数组用于预处理
            image_array = np.array(original_image)
            
            # 创建元信息字典
            meta = {
                'original_size': original_image.size,
                'image_path': image_path,
                'channels': len(original_image.getbands())
            }
            
            logger.debug(f"成功加载图像: {image_path}, 尺寸: {original_image.size}")
            return original_image, image_array, meta
            
        except Exception as e:
            logger.error(f"加载图像失败: {image_path}, 错误: {e}")
            raise
    
    @staticmethod
    def draw_lore_tsr_predictions(
        image: Image.Image, 
        predictions: Dict[str, Any], 
        style_config: Dict[str, Any]
    ) -> Image.Image:
        """
        在图像上绘制LORE-TSR预测结果
        
        Args:
            image: 目标图像
            predictions: 预测结果字典
            style_config: 样式配置
            
        Returns:
            Image.Image: 绘制后的图像
        """
        # 创建图像副本
        result_image = image.copy()
        
        # 转换为OpenCV格式进行绘制
        cv_image = cv2.cvtColor(np.array(result_image), cv2.COLOR_RGB2BGR)
        
        # 绘制边界框和逻辑坐标
        if 'bboxes' in predictions and 'logic_coords' in predictions:
            bboxes = predictions['bboxes']
            logic_coords = predictions['logic_coords']
            
            for i, (bbox, logic) in enumerate(zip(bboxes, logic_coords)):
                cv_image = LoreTsrImageUtils.draw_4ps_bbox_with_logic(
                    cv_image, bbox, logic, style_config
                )
        
        # 绘制角点箭头
        if 'corners' in predictions:
            corners = predictions['corners']
            for corner in corners:
                cv_image = LoreTsrImageUtils.draw_corner_arrows(
                    cv_image, corner, style_config.get('corner_arrow_colors', []), 
                    style_config.get('arrow_thickness', 1)
                )
        
        # 转换回PIL格式
        result_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))
        return result_image
    
    @staticmethod
    def draw_4ps_bbox_with_logic(
        image: np.ndarray, 
        bbox: List[float], 
        logic_coords: List[int], 
        style: Dict[str, Any]
    ) -> np.ndarray:
        """
        绘制四点边界框并标注逻辑坐标信息
        基于LORE-TSR原始的add_4ps_coco_bbox函数实现
        
        Args:
            image: 目标图像（OpenCV格式）
            bbox: 四点边界框坐标 [x1,y1,x2,y2,x3,y3,x4,y4]
            logic_coords: 逻辑坐标 [start_row, end_row, start_col, end_col]
            style: 样式配置
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        # 转换坐标为整数
        bbox = np.array(bbox, dtype=np.int32)
        
        # 获取样式配置
        bbox_color = tuple(style.get('bbox_color', [0, 255, 0]))  # 默认绿色
        line_thickness = style.get('line_thickness', 2)
        text_color = tuple(style.get('logic_text_color', [255, 255, 0]))  # 默认黄色
        text_size = style.get('text_size', 0.3)
        
        # 绘制四点边界框
        # 连接四个点形成四边形
        points = bbox.reshape(4, 2)
        cv2.polylines(image, [points], True, bbox_color, line_thickness)
        
        # 格式化逻辑坐标文本
        if logic_coords is not None and len(logic_coords) >= 4:
            logic_text = f"({logic_coords[0]},{logic_coords[1]},{logic_coords[2]},{logic_coords[3]})"
            
            # 计算文本位置（在边界框左上角）
            text_position = (bbox[0], bbox[1] - 5)
            
            # 获取文本尺寸
            font = cv2.FONT_HERSHEY_SIMPLEX
            (text_width, text_height), baseline = cv2.getTextSize(
                logic_text, font, text_size, 1
            )
            
            # 绘制文本背景矩形
            cv2.rectangle(
                image,
                (text_position[0], text_position[1] - text_height - baseline),
                (text_position[0] + text_width, text_position[1] + baseline),
                bbox_color, -1
            )
            
            # 绘制逻辑坐标文本
            cv2.putText(
                image, logic_text, text_position, font, text_size, 
                (0, 0, 0), thickness=1, lineType=cv2.LINE_AA
            )
        
        return image
    
    @staticmethod
    def draw_corner_arrows(
        image: np.ndarray, 
        corner: List[float], 
        colors: List[List[int]], 
        thickness: int
    ) -> np.ndarray:
        """
        绘制角点方向箭头，显示表格结构的方向信息
        基于LORE-TSR原始的add_corner函数实现
        
        Args:
            image: 目标图像（OpenCV格式）
            corner: 角点信息 [x,y,dx1,dy1,dx2,dy2,dx3,dy3,dx4,dy4]
            colors: 四个方向的箭头颜色 [[r,g,b], ...]
            thickness: 箭头线条粗细
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        if len(corner) < 10:
            logger.warning(f"角点数据不完整，需要10个值，实际得到{len(corner)}个")
            return image
        
        # 中心点坐标
        center_x, center_y = int(corner[0]), int(corner[1])
        
        # 绘制中心点
        cv2.circle(image, (center_x, center_y), 3, (255, 0, 0), -1)
        
        # 默认颜色（如果colors不足）
        default_colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 0, 0)]
        
        # 绘制四个方向的箭头
        directions = [
            (corner[2], corner[3]),   # 右方向
            (corner[4], corner[5]),   # 下方向  
            (corner[6], corner[7]),   # 左方向
            (corner[8], corner[9])    # 上方向
        ]
        
        for i, (dx, dy) in enumerate(directions):
            # 检查向量长度是否超过阈值
            if abs(int(dx) + int(dy)) > 10:
                # 计算终点坐标
                end_x = int(center_x + dx)
                end_y = int(center_y + dy)
                
                # 获取颜色
                if i < len(colors) and len(colors[i]) >= 3:
                    color = tuple(colors[i][:3])
                else:
                    color = default_colors[i]
                
                # 绘制箭头
                cv2.arrowedLine(
                    image, 
                    (center_x, center_y), 
                    (end_x, end_y), 
                    color, 
                    thickness=thickness, 
                    line_type=cv2.LINE_4, 
                    shift=0, 
                    tipLength=0.02
                )
        
        return image
    
    @staticmethod
    def create_logic_axis_visualization(
        logic_axis: np.ndarray, 
        image_size: Tuple[int, int]
    ) -> Image.Image:
        """
        创建Processor输出的逻辑轴向可视化
        
        Args:
            logic_axis: 逻辑轴向张量 [batch_size, seq_len, 4] 或 [seq_len, 4]
            image_size: 目标图像尺寸 (width, height)
            
        Returns:
            Image.Image: 逻辑轴向可视化图像
        """
        width, height = image_size
        
        # 创建空白画布
        canvas = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # 处理张量维度
        if len(logic_axis.shape) == 3:
            logic_axis = logic_axis[0]  # 取第一个batch
        
        # 如果是torch张量，转换为numpy
        if hasattr(logic_axis, 'detach'):
            logic_axis = logic_axis.detach().cpu().numpy()
        
        # 绘制网格和逻辑坐标
        for i, coords in enumerate(logic_axis):
            if len(coords) >= 4:
                start_row, end_row, start_col, end_col = coords[:4]
                
                # 计算网格位置（简化版本）
                cell_width = width // 20  # 假设最大20列
                cell_height = height // 20  # 假设最大20行
                
                x1 = int(start_col * cell_width)
                y1 = int(start_row * cell_height)
                x2 = int((end_col + 1) * cell_width)
                y2 = int((end_row + 1) * cell_height)
                
                # 确保坐标在画布范围内
                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(width, x2), min(height, y2)
                
                # 绘制网格单元
                color = (100 + (i * 30) % 155, 100 + (i * 50) % 155, 100 + (i * 70) % 155)
                cv2.rectangle(canvas, (x1, y1), (x2, y2), color, 2)
                
                # 标注行列信息
                text = f"({int(start_row)},{int(end_row)},{int(start_col)},{int(end_col)})"
                cv2.putText(canvas, text, (x1 + 5, y1 + 15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)
        
        # 转换为PIL图像
        return Image.fromarray(cv2.cvtColor(canvas, cv2.COLOR_BGR2RGB))
    
    @staticmethod
    def format_logic_coordinates(logic_coords: List[int]) -> str:
        """
        格式化逻辑坐标为显示文本
        
        Args:
            logic_coords: 逻辑坐标 [start_row, end_row, start_col, end_col]
            
        Returns:
            str: 格式化的文本
        """
        if len(logic_coords) >= 4:
            return f"({logic_coords[0]},{logic_coords[1]},{logic_coords[2]},{logic_coords[3]})"
        else:
            return f"({','.join(map(str, logic_coords))})"
```

#### 2. 更新可视化模块导入
修改 `modules/visualization/__init__.py`，添加新模块的导入：

```python
# 在现有导入后添加
from .lore_tsr_image_utils import LoreTsrImageUtils
```

### 受影响的现有模块
- **可视化模块**: 在现有可视化目录中添加新的图像处理工具
- **模块导入系统**: 更新__init__.py确保新模块可被正确导入

### 复用已有代码
- **参考现有图像工具**: 借鉴 `image_utils.py` 中的图像处理模式
- **复用OpenCV和PIL**: 使用现有的图像处理库
- **保持接口一致性**: 与现有可视化器保持相同的接口设计模式

### 如何验证

#### 验证命令1 - 模块导入测试
```shell
python -c "
import sys; sys.path.append('.');
from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils;
print('✅ LORE-TSR图像工具导入成功');

# 测试类方法可用性
methods = ['load_and_preprocess_image', 'draw_lore_tsr_predictions', 
           'draw_4ps_bbox_with_logic', 'draw_corner_arrows', 
           'create_logic_axis_visualization', 'format_logic_coordinates'];
for method in methods:
    has_method = hasattr(LoreTsrImageUtils, method);
    print(f'  - {method}: {has_method}');

print('🎉 步骤9.2图像工具验证通过')
"
```

#### 验证命令2 - 基础功能测试
```shell
python -c "
import sys; sys.path.append('.');
import numpy as np;
from PIL import Image;
from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils;

print('✅ 开始基础功能测试');

# 测试格式化逻辑坐标
logic_coords = [1, 2, 3, 4];
formatted = LoreTsrImageUtils.format_logic_coordinates(logic_coords);
print(f'  - 逻辑坐标格式化: {formatted}');

# 测试创建逻辑轴向可视化
logic_axis = np.array([[1, 2, 3, 4], [2, 3, 4, 5]]);
vis_image = LoreTsrImageUtils.create_logic_axis_visualization(logic_axis, (400, 300));
print(f'  - 逻辑轴向可视化: {type(vis_image).__name__}, 尺寸: {vis_image.size}');

# 测试四点边界框绘制（使用虚拟数据）
test_image = np.ones((300, 400, 3), dtype=np.uint8) * 255;
bbox = [50, 50, 150, 50, 150, 100, 50, 100];
logic = [1, 2, 3, 4];
style = {'bbox_color': [0, 255, 0], 'line_thickness': 2, 'logic_text_color': [255, 255, 0], 'text_size': 0.3};
result = LoreTsrImageUtils.draw_4ps_bbox_with_logic(test_image, bbox, logic, style);
print(f'  - 四点边界框绘制: {result.shape}');

print('🎉 步骤9.2基础功能验证通过')
"
```

#### 验证命令3 - 项目可运行性测试
```shell
python -c "
import sys; sys.path.append('.');
# 测试可视化器仍然可以正常导入
from modules.visualization.lore_tsr_visualizer import LoreTsrVisualizer;
print('✅ 主可视化器导入正常');

# 测试新的图像工具可以被可视化器使用
from modules.visualization.lore_tsr_image_utils import LoreTsrImageUtils;
print('  - 图像工具可用于可视化器');

# 测试配置系统完整性
from omegaconf import OmegaConf;
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml');
print('  - 配置系统正常工作');
print('  - 项目保持可运行状态');

print('🎉 步骤9.2项目可运行性验证通过')
"
```

## 🔄 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代9步骤9.2 - LORE-TSR图像处理工具实现

    subgraph "Source: LORE-TSR/lib/utils/debugger.py"
        direction TB
        S1["add_4ps_coco_bbox<br/>四点边界框绘制"]
        S2["add_corner<br/>角点箭头绘制"]
        S3["颜色主题控制"]
        S4["OpenCV绘制逻辑"]
    end

    subgraph "Target: train-anything图像处理工具"
        direction TB
        T1["modules/visualization/lore_tsr_image_utils.py"]
        T1_1["draw_4ps_bbox_with_logic"]
        T1_2["draw_corner_arrows"]
        T1_3["create_logic_axis_visualization"]
        T1_4["load_and_preprocess_image"]
        T1_5["draw_lore_tsr_predictions"]
        
        T1 --> T1_1
        T1 --> T1_2
        T1 --> T1_3
        T1 --> T1_4
        T1 --> T1_5
    end

    subgraph "现有基础设施"
        direction LR
        REF1["image_utils.py<br/>现有图像工具"]
        REF2["OpenCV/PIL库"]
        REF3["步骤9.1可视化器框架"]
    end

    %% 迁移映射（步骤9.2）
    S1 -- "复制保留<br/>适配PIL/OpenCV" --> T1_1
    S2 -- "复制保留<br/>保持绘制逻辑" --> T1_2
    S3 -- "重构适配<br/>配置驱动" --> T1_5
    S4 -- "复制保留<br/>OpenCV操作" --> T1_1

    %% 复用关系
    REF1 -.-> T1_4
    REF2 -.-> T1_1
    REF2 -.-> T1_2
    REF3 -.-> T1

    %% 新增功能
    T1_3 -.- NEW1["新增：Processor输出可视化"]
    T1_4 -.- NEW2["新增：图像预处理"]

    %% 后续步骤（灰色表示未实现）
    T1 -.-> NEXT1["步骤9.3：visualization_utils.py"]
    T1 -.-> NEXT2["步骤9.4：集成到训练循环"]

    style NEW1 fill:#e1f5fe
    style NEW2 fill:#e1f5fe
    style NEXT1 fill:#f9f9f9,stroke:#ccc,stroke-dasharray: 5 5
    style NEXT2 fill:#f9f9f9,stroke:#ccc,stroke-dasharray: 5 5
```

## 📝 步骤完成标准

### 功能验收标准
1. ✅ **图像工具创建成功**: LoreTsrImageUtils类能够正确实例化和调用所有方法
2. ✅ **核心绘制功能**: 四点边界框和角点箭头绘制功能正常工作
3. ✅ **逻辑坐标处理**: 逻辑坐标格式化和可视化功能正常
4. ✅ **模块导入正常**: 新模块能被正确导入和使用

### 技术验收标准
1. ✅ **代码复用**: 最大程度复用LORE-TSR原始绘制逻辑，保持视觉效果一致
2. ✅ **接口设计**: 提供清晰的静态方法接口，便于可视化器调用
3. ✅ **错误处理**: 完善的异常处理和日志记录
4. ✅ **性能优化**: 高效的图像处理，支持批量操作

### 为下一步准备
- **步骤9.3**: 实现可视化辅助工具（visualization_utils.py）
- **步骤9.4**: 集成完整的可视化流程到训练循环
- **接口预留**: 已为后续集成预留了标准化的接口

---

**计划制定完成时间**: 2025-07-20  
**预估执行时间**: 1-1.5个工作日  
**风险评估**: 中等（主要是OpenCV绘制逻辑的适配）  
**依赖关系**: 依赖步骤9.1的基础框架（已完成）
