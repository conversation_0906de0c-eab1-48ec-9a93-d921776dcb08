#!/usr/bin/env python3
"""
LORE-TSR可视化辅助工具
提供数据格式转换、坐标变换、配置解析等支持功能

Time: 2025-07-20
Author: Migration from LORE-TSR to train-anything
Description: 基于LORE-TSR的ctdet.py::show_results和trains/ctdet.py::debug函数逻辑
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from omegaconf import DictConfig

# 导入现有的坐标变换函数
try:
    from .lore_image_utils import transform_preds, transform_preds_upper_left
    _TRANSFORM_UTILS_AVAILABLE = True
except ImportError:
    _TRANSFORM_UTILS_AVAILABLE = False

logger = logging.getLogger(__name__)


class VisualizationUtils:
    """
    LORE-TSR可视化辅助工具类
    
    提供数据格式转换、坐标变换、配置解析等支持功能，
    基于LORE-TSR原始的数据处理逻辑实现
    """
    
    @staticmethod
    def convert_model_output_to_predictions(
        outputs: Dict[str, torch.Tensor], 
        meta: Dict[str, Any],
        opt: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        将模型原始输出转换为可视化所需的预测格式
        基于LORE-TSR的ctdet.py::show_results函数逻辑
        
        Args:
            outputs: 模型输出字典，包含hm, wh, reg, ax, cr等
            meta: 图像元信息，包含变换参数
            opt: 可选的配置对象，包含阈值等参数
            
        Returns:
            Dict[str, Any]: 标准化预测结果
        """
        try:
            predictions = {
                'bboxes': [],
                'logic_coords': [],
                'corners': [],
                'scores': [],
                'classes': []
            }
            
            # 检查输出格式
            if not isinstance(outputs, dict):
                logger.warning("模型输出不是字典格式")
                return predictions
            
            # 提取关键输出
            hm = outputs.get('hm', None)
            wh = outputs.get('wh', None)
            reg = outputs.get('reg', None)
            ax = outputs.get('ax', None)
            cr = outputs.get('cr', None)
            
            if hm is None:
                logger.warning("模型输出中缺少热力图(hm)")
                return predictions
            
            # 基于LORE-TSR的解码逻辑进行简化处理
            # 这里提供基础框架，实际使用时需要集成完整的ctdet_4ps_decode逻辑
            batch_size = hm.shape[0]
            
            # 获取阈值参数
            center_thresh = getattr(opt, 'center_thresh', 0.3) if opt else 0.3
            vis_thresh = getattr(opt, 'vis_thresh', 0.3) if opt else 0.3
            down_ratio = getattr(opt, 'down_ratio', 4) if opt else 4
            
            for batch_idx in range(batch_size):
                # 提取当前批次的预测
                batch_hm = hm[batch_idx] if hm is not None else None
                
                # 简化的热力图峰值检测（基于LORE-TSR的debug函数逻辑）
                if batch_hm is not None:
                    # 转换为numpy进行处理
                    hm_np = batch_hm.detach().cpu().numpy()
                    
                    # 处理多类别热力图
                    if len(hm_np.shape) == 3:
                        # 取第一个类别（表格类别）
                        hm_np = hm_np[0]
                    
                    # 简单的峰值检测（实际应使用完整的ctdet_decode）
                    peaks = np.where(hm_np > center_thresh)
                    
                    for i in range(len(peaks[0])):
                        y, x = peaks[0][i], peaks[1][i]
                        score = float(hm_np[y, x])
                        
                        if score > vis_thresh:
                            # 构造简化的边界框（实际应使用wh回归和完整解码）
                            # 基于LORE-TSR的坐标缩放逻辑
                            x_scaled = x * down_ratio
                            y_scaled = y * down_ratio
                            
                            # 简化的四点边界框（实际应从模型输出解码）
                            bbox = [
                                x_scaled - 20, y_scaled - 10,  # 左上
                                x_scaled + 20, y_scaled - 10,  # 右上
                                x_scaled + 20, y_scaled + 10,  # 右下
                                x_scaled - 20, y_scaled + 10   # 左下
                            ]
                            
                            # 简化的逻辑坐标（实际应从ax输出解码）
                            logic_coord = [0, 1, 0, 1]
                            
                            predictions['bboxes'].append(bbox)
                            predictions['logic_coords'].append(logic_coord)
                            predictions['scores'].append(score)
                            predictions['classes'].append(1)  # 表格类别
            
            logger.debug(f"转换得到{len(predictions['bboxes'])}个预测结果")
            return predictions
            
        except Exception as e:
            logger.error(f"模型输出转换失败: {e}")
            return {
                'bboxes': [],
                'logic_coords': [],
                'corners': [],
                'scores': [],
                'classes': []
            }
    
    @staticmethod
    def convert_processor_output_to_logic_coords(
        logic_axis: torch.Tensor
    ) -> List[List[int]]:
        """
        将Processor输出的逻辑轴向转换为逻辑坐标列表
        基于LORE-TSR的逻辑坐标处理逻辑
        
        Args:
            logic_axis: 逻辑轴向张量 [batch_size, seq_len, 4] 或 [seq_len, 4]
            
        Returns:
            List[List[int]]: 逻辑坐标列表
        """
        try:
            # 处理张量维度
            if len(logic_axis.shape) == 3:
                logic_axis = logic_axis[0]  # 取第一个batch
            
            # 转换为numpy
            if hasattr(logic_axis, 'detach'):
                logic_axis = logic_axis.detach().cpu().numpy()
            
            # 转换为整数列表（基于LORE-TSR的logi处理逻辑）
            logic_coords = []
            for coords in logic_axis:
                if len(coords) >= 4:
                    # 基于LORE-TSR的int转换逻辑
                    logic_coord = [int(coords[0]), int(coords[1]), int(coords[2]), int(coords[3])]
                    logic_coords.append(logic_coord)
            
            logger.debug(f"转换得到{len(logic_coords)}个逻辑坐标")
            return logic_coords
            
        except Exception as e:
            logger.error(f"Processor输出转换失败: {e}")
            return []
    
    @staticmethod
    def transform_coordinates(
        coords: List[float], 
        meta: Dict[str, Any],
        use_upper_left: bool = False
    ) -> List[float]:
        """
        应用坐标变换，将模型输出坐标转换为原图坐标
        基于LORE-TSR的transform_preds函数逻辑
        
        Args:
            coords: 坐标列表 [x1, y1, x2, y2, ...]
            meta: 包含变换参数的元信息
            use_upper_left: 是否使用左上角模式
            
        Returns:
            List[float]: 变换后的坐标
        """
        try:
            if not _TRANSFORM_UTILS_AVAILABLE:
                logger.warning("坐标变换工具不可用，使用简化变换")
                return VisualizationUtils._simple_coordinate_transform(coords, meta)
            
            # 获取变换参数（基于LORE-TSR的meta格式）
            c = meta.get('c', [0, 0])  # 中心点
            s = meta.get('s', [1, 1])  # 缩放
            output_height = meta.get('out_height', 128)
            output_width = meta.get('out_width', 128)
            
            # 将坐标转换为numpy数组格式
            coords_array = np.array(coords).reshape(-1, 2)
            
            # 使用LORE-TSR的坐标变换函数
            if use_upper_left:
                transformed_coords = transform_preds_upper_left(
                    coords_array, c, s, (output_width, output_height)
                )
            else:
                transformed_coords = transform_preds(
                    coords_array, c, s, (output_width, output_height)
                )
            
            # 转换回列表格式
            result = transformed_coords.flatten().tolist()
            return result
            
        except Exception as e:
            logger.error(f"坐标变换失败: {e}")
            return coords
    
    @staticmethod
    def _simple_coordinate_transform(
        coords: List[float], 
        meta: Dict[str, Any]
    ) -> List[float]:
        """
        简化的坐标变换（当完整变换工具不可用时使用）
        
        Args:
            coords: 坐标列表
            meta: 元信息
            
        Returns:
            List[float]: 变换后的坐标
        """
        try:
            # 获取缩放参数
            output_size = meta.get('output_size', (128, 128))
            input_size = meta.get('input_size', (512, 512))
            
            scale_x = input_size[0] / output_size[0]
            scale_y = input_size[1] / output_size[1]
            
            transformed_coords = []
            for i in range(0, len(coords), 2):
                if i + 1 < len(coords):
                    x = coords[i] * scale_x
                    y = coords[i + 1] * scale_y
                    transformed_coords.extend([x, y])
            
            return transformed_coords
            
        except Exception as e:
            logger.error(f"简化坐标变换失败: {e}")
            return coords

    @staticmethod
    def parse_visualization_config(config: DictConfig) -> Dict[str, Any]:
        """
        解析可视化配置，提取相关参数
        基于train-anything的配置系统设计

        Args:
            config: OmegaConf配置对象

        Returns:
            Dict[str, Any]: 解析后的配置字典
        """
        try:
            vis_config = {}

            # 基础可视化配置
            if hasattr(config, 'visualization'):
                vis = config.visualization
                vis_config.update({
                    'enabled': getattr(vis, 'enabled', True),
                    'sample_images_dir': getattr(vis, 'sample_images_dir', 'assets/test_images'),
                    'max_samples': getattr(vis, 'max_samples', 10),
                    'output_dir': getattr(vis, 'output_dir', None),
                    'frequency': getattr(vis, 'frequency', 1),
                    'debugger_theme': getattr(vis, 'debugger_theme', 'white')
                })

                # LORE-TSR特有配置
                if hasattr(vis, 'lore_tsr'):
                    lore_tsr = vis.lore_tsr
                    vis_config['lore_tsr'] = {
                        'show_logic_coordinates': getattr(lore_tsr, 'show_logic_coordinates', True),
                        'show_corner_arrows': getattr(lore_tsr, 'show_corner_arrows', True),
                        'show_processor_output': getattr(lore_tsr, 'show_processor_output', True),
                        'use_4ps_bbox': getattr(lore_tsr, 'use_4ps_bbox', True)
                    }

                # 样式配置
                if hasattr(vis, 'style'):
                    style = vis.style
                    vis_config['style'] = {
                        'bbox_color': getattr(style, 'bbox_color', [0, 255, 0]),
                        'logic_text_color': getattr(style, 'logic_text_color', [255, 255, 0]),
                        'corner_arrow_colors': getattr(style, 'corner_arrow_colors',
                                                     [[0, 0, 255], [0, 255, 0], [255, 0, 0], [0, 0, 0]]),
                        'transparency': getattr(style, 'transparency', 0.8),
                        'line_thickness': getattr(style, 'line_thickness', 2),
                        'arrow_thickness': getattr(style, 'arrow_thickness', 1),
                        'text_size': getattr(style, 'text_size', 0.3)
                    }

                # 热力图配置
                if hasattr(vis, 'heatmap'):
                    heatmap = vis.heatmap
                    vis_config['heatmap'] = {
                        'colormap': getattr(heatmap, 'colormap', 'jet'),
                        'normalize': getattr(heatmap, 'normalize', True),
                        'threshold': getattr(heatmap, 'threshold', 0.1)
                    }

            logger.debug("可视化配置解析完成")
            return vis_config

        except Exception as e:
            logger.error(f"配置解析失败: {e}")
            return {}

    @staticmethod
    def validate_prediction_format(predictions: Dict[str, Any]) -> bool:
        """
        验证预测结果格式是否正确
        基于LORE-TSR的数据格式要求

        Args:
            predictions: 预测结果字典

        Returns:
            bool: 格式是否正确
        """
        try:
            required_keys = ['bboxes', 'logic_coords', 'scores', 'classes']

            # 检查必需的键
            for key in required_keys:
                if key not in predictions:
                    logger.warning(f"预测结果缺少必需的键: {key}")
                    return False

            # 检查数据长度一致性
            bboxes = predictions['bboxes']
            logic_coords = predictions['logic_coords']
            scores = predictions['scores']
            classes = predictions['classes']

            lengths = [len(bboxes), len(logic_coords), len(scores), len(classes)]
            if len(set(lengths)) > 1:
                logger.warning(f"预测结果数据长度不一致: {lengths}")
                return False

            # 检查边界框格式（基于LORE-TSR的四点边界框格式）
            for bbox in bboxes:
                if not isinstance(bbox, (list, tuple)) or len(bbox) != 8:
                    logger.warning(f"边界框格式错误，应为8个坐标点: {bbox}")
                    return False

            # 检查逻辑坐标格式（基于LORE-TSR的logi格式）
            for logic in logic_coords:
                if not isinstance(logic, (list, tuple)) or len(logic) != 4:
                    logger.warning(f"逻辑坐标格式错误，应为4个值: {logic}")
                    return False

            logger.debug("预测结果格式验证通过")
            return True

        except Exception as e:
            logger.error(f"预测结果格式验证失败: {e}")
            return False

    @staticmethod
    def create_color_palette(num_colors: int) -> List[List[int]]:
        """
        创建颜色调色板
        基于LORE-TSR的颜色方案设计

        Args:
            num_colors: 需要的颜色数量

        Returns:
            List[List[int]]: 颜色列表，每个颜色为[R, G, B]
        """
        try:
            colors = []

            # 基于LORE-TSR的预定义颜色（参考debugger.py的颜色设置）
            base_colors = [
                [255, 0, 0],    # 红色
                [0, 255, 0],    # 绿色
                [0, 0, 255],    # 蓝色
                [255, 255, 0],  # 黄色
                [255, 0, 255],  # 品红
                [0, 255, 255],  # 青色
                [255, 128, 0],  # 橙色
                [128, 0, 255],  # 紫色
            ]

            # 如果需要的颜色数量不超过基础颜色，直接返回
            if num_colors <= len(base_colors):
                return base_colors[:num_colors]

            # 否则生成更多颜色
            colors.extend(base_colors)

            # 生成额外的颜色（使用HSV色彩空间）
            for i in range(len(base_colors), num_colors):
                # 均匀分布的色相
                hue = (i * 360 // num_colors) % 360
                saturation = 255
                value = 255

                # 简化的HSV到RGB转换
                c = value * saturation // 255
                x = c * (1 - abs((hue // 60) % 2 - 1))
                m = value - c

                if 0 <= hue < 60:
                    r, g, b = c, x, 0
                elif 60 <= hue < 120:
                    r, g, b = x, c, 0
                elif 120 <= hue < 180:
                    r, g, b = 0, c, x
                elif 180 <= hue < 240:
                    r, g, b = 0, x, c
                elif 240 <= hue < 300:
                    r, g, b = x, 0, c
                else:
                    r, g, b = c, 0, x

                colors.append([int(r + m), int(g + m), int(b + m)])

            return colors[:num_colors]

        except Exception as e:
            logger.error(f"颜色调色板创建失败: {e}")
            return [[255, 0, 0]] * num_colors  # 返回红色作为默认

    @staticmethod
    def extract_corner_data(corner_output: np.ndarray, vis_thresh_corner: float = 0.3) -> List[List[float]]:
        """
        从角点输出中提取可视化数据
        基于LORE-TSR的ctdet.py::show_results中的角点处理逻辑

        Args:
            corner_output: 角点输出数组
            vis_thresh_corner: 角点可视化阈值

        Returns:
            List[List[float]]: 角点数据列表
        """
        try:
            corners = []

            if corner_output is None or len(corner_output) == 0:
                return corners

            # 基于LORE-TSR的角点处理逻辑
            m, n = corner_output.shape

            for i in range(m):
                # 检查角点置信度（基于LORE-TSR的vis_thresh_corner逻辑）
                if len(corner_output[i]) > 10 and corner_output[i, 10] > vis_thresh_corner:
                    # 提取角点坐标（基于LORE-TSR的corner格式）
                    corner_data = []

                    # 中心点
                    center_x = corner_output[i, 8] if len(corner_output[i]) > 8 else 0
                    center_y = corner_output[i, 9] if len(corner_output[i]) > 9 else 0
                    corner_data.extend([center_x, center_y])

                    # 四个方向的向量（基于LORE-TSR的corner格式）
                    for w in range(4):
                        if 2*w + 1 < len(corner_output[i]):
                            dx = corner_output[i, 2*w] - center_x
                            dy = corner_output[i, 2*w + 1] - center_y
                            corner_data.extend([dx, dy])
                        else:
                            corner_data.extend([0, 0])

                    corners.append(corner_data)

            logger.debug(f"提取得到{len(corners)}个角点")
            return corners

        except Exception as e:
            logger.error(f"角点数据提取失败: {e}")
            return []
