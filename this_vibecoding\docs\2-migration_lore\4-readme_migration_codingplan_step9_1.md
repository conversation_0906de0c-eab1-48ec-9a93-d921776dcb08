# LORE-TSR 迁移项目 - 迭代9步骤9.1编码计划

**文档版本**: v1.0  
**创建日期**: 2025-07-20  
**当前迭代**: 迭代9 - 可视化功能扩展  
**当前步骤**: 步骤9.1 - 创建可视化基础框架和配置扩展  
**优先级**: P2（可选）  

## 📋 动态迁移蓝图更新

### 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 | 当前迭代 | 复杂度 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | 重构适配 | 迭代1 | **复杂** | **已完成** |
| `main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | 重构适配 | 迭代1,3 | **复杂** | **已完成** |
| `lib/models/model.py` | `networks/lore_tsr/lore_tsr_model.py` | 复制保留 | 迭代2 | **复杂** | **已完成** |
| `lib/models/losses.py` | `networks/lore_tsr/lore_tsr_loss.py` | 复制保留 | 迭代4 | 简单 | **已完成** |
| `lib/models/classifier.py` | `networks/lore_tsr/processor.py` | 复制保留 | 迭代6 | **复杂** | **已完成** |
| `lib/models/transformer.py` | `networks/lore_tsr/transformer.py` | 复制保留 | 迭代6 | **复杂** | **已完成** |
| `lib/models/networks/fpn_resnet_half.py` | `networks/lore_tsr/backbones/fpn_resnet_half.py` | 复制保留 | 迭代2 | 简单 | **已完成** |
| `lib/datasets/dataset/table_mid.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | 重构适配 | 迭代5 | **复杂** | **已完成** |
| `lib/utils/post_process.py` | `modules/utils/lore_tsr/post_process.py` | 复制保留 | 迭代11 | 简单 | 未开始 |
| `lib/models/networks/DCNv2/` | `external/lore_tsr/DCNv2/` | 复制隔离 | 迭代7 | 简单 | **已完成** |
| `lib/models/networks/NMS/` | `external/lore_tsr/NMS/` | 复制隔离 | 迭代7 | 简单 | **已完成** |
| **权重处理工具** | `modules/utils/lore_tsr/weight_utils.py` | **新建** | **迭代8** | **简单** | **已完成** |
| **权重转换器** | `modules/utils/lore_tsr/weight_converter.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重加载器** | `modules/utils/lore_tsr/weight_loader.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重验证器** | `modules/utils/lore_tsr/weight_validator.py` | **新建** | **迭代8** | **复杂** | **已完成** |
| **权重转换脚本** | `cmd_scripts/train_table_structure/convert_lore_weights.py` | **新建** | **迭代8** | **简单** | **已完成** |
| `lib/utils/debugger.py` | `modules/visualization/lore_tsr_visualizer.py` | 重构适配 | **迭代9** | **复杂** | **进行中** |
| `lib/utils/debugger.py::add_4ps_coco_bbox` | `modules/visualization/lore_tsr_image_utils.py` | 复制保留 | **迭代9** | **复杂** | **未开始** |
| `lib/utils/debugger.py::add_corner` | `modules/visualization/lore_tsr_image_utils.py` | 复制保留 | **迭代9** | **复杂** | **未开始** |
| **可视化辅助工具** | `modules/utils/lore_tsr/visualization_utils.py` | **新建** | **迭代9** | **复杂** | **未开始** |

### 目标目录结构树

```text
train-anything/
├── configs/table_structure_recognition/lore_tsr/
│   └── lore_tsr_config.yaml                         # [已存在，需扩展]
├── training_loops/table_structure_recognition/
│   └── train_lore_tsr.py                            # [已存在]
├── networks/lore_tsr/                               # [已存在]
│   ├── __init__.py                                  # [已存在]
│   ├── lore_tsr_model.py                            # [已存在]
│   ├── lore_tsr_loss.py                             # [已存在]
│   ├── processor.py                                 # [已存在]
│   ├── transformer.py                               # [已存在]
│   ├── backbones/                                   # [已存在]
│   │   ├── __init__.py                              # [已存在]
│   │   ├── fpn_resnet_half.py                       # [已存在]
│   │   ├── fpn_resnet.py                            # [已存在]
│   │   ├── fpn_mask_resnet_half.py                  # [已存在]
│   │   ├── fpn_mask_resnet.py                       # [已存在]
│   │   └── pose_dla_dcn.py                          # [已存在]
│   └── heads/                                       # [已存在]
│       ├── __init__.py                              # [已存在]
│       └── lore_tsr_head.py                         # [已存在]
├── my_datasets/table_structure_recognition/         # [已存在]
│   ├── lore_tsr_dataset.py                          # [已存在]
│   ├── lore_tsr_transforms.py                       # [已存在]
│   └── lore_tsr_target_preparation.py               # [已存在]
├── modules/utils/lore_tsr/                          # [已存在]
│   ├── __init__.py                                  # [已存在]
│   ├── weight_utils.py                              # [已存在]
│   ├── weight_converter.py                          # [已存在]
│   ├── weight_loader.py                             # [已存在]
│   ├── weight_validator.py                          # [已存在]
│   ├── visualization_utils.py                       # [待创建]
│   ├── post_process.py                              # [待创建]
│   ├── oracle_utils.py                              # [待创建]
│   └── eval_utils.py                                # [待创建]
├── modules/visualization/                           # [已存在]
│   ├── lore_tsr_visualizer.py                       # [待创建]
│   ├── lore_tsr_image_utils.py                      # [待创建]
│   ├── image_utils.py                               # [已存在]
│   ├── table_structure_visualizer.py               # [已存在，参考]
│   └── table_structure_visualizer_ms.py            # [已存在，参考]
├── cmd_scripts/train_table_structure/               # [已存在]
│   ├── convert_lore_weights.py                      # [已存在]
│   └── lore_tsr_train.sh                            # [待创建]
└── external/lore_tsr/                               # [已存在]
    ├── DCNv2/                                       # [已存在]
    ├── NMS/                                         # [已存在]
    └── cocoapi/                                     # [已存在]
```

## 🎯 步骤9.1详细计划

### 步骤标题
**迭代9步骤9.1: 创建可视化基础框架和配置扩展**

### 当前迭代
**迭代9 - 可视化功能扩展**

### 影响文件
1. `modules/visualization/lore_tsr_visualizer.py` - **新建**，LORE-TSR专用可视化器基础框架
2. `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` - **扩展**，添加可视化配置项
3. `modules/visualization/__init__.py` - **可能修改**，确保新模块可导入

### 具体操作

#### 1. 创建基础可视化器框架
创建 `modules/visualization/lore_tsr_visualizer.py`，实现基础的可视化器类框架：

```python
#!/usr/bin/env python3
"""
LORE-TSR专用可视化器
基于train-anything框架的可视化系统扩展
"""

import torch
from typing import Any, Optional, Dict, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class LoreTsrVisualizer:
    """LORE-TSR专用可视化器"""
    
    def __init__(self, config: Any, device: torch.device, weight_dtype: torch.dtype):
        """
        初始化LORE-TSR可视化器
        
        Args:
            config: 配置对象
            device: 计算设备
            weight_dtype: 权重数据类型
        """
        self.config = config
        self.device = device
        self.weight_dtype = weight_dtype
        self.vis_config = getattr(config, 'visualization', None)
        self.visualization_counter = 0
        
        logger.info("LORE-TSR可视化器已初始化")
    
    def visualize_validation_samples(
        self, 
        model: torch.nn.Module, 
        global_step: int, 
        accelerator: Any
    ) -> None:
        """
        可视化验证样本的主入口函数
        
        Args:
            model: 训练好的LORE-TSR模型
            global_step: 当前训练步数
            accelerator: accelerate框架对象
        """
        if not self._check_visualization_enabled():
            logger.debug("可视化功能未启用，跳过")
            return
        
        if not self._should_visualize(global_step):
            logger.debug(f"步数{global_step}不需要可视化，跳过")
            return
        
        logger.info(f"开始执行步数{global_step}的可视化")
        
        # TODO: 后续步骤将实现具体的可视化逻辑
        logger.info("可视化功能框架已就绪，等待具体实现")
        
        self.visualization_counter += 1
    
    def _check_visualization_enabled(self) -> bool:
        """检查可视化功能是否启用"""
        if self.vis_config is None:
            return False
        return getattr(self.vis_config, 'enabled', False)
    
    def _should_visualize(self, global_step: int) -> bool:
        """检查当前步数是否需要执行可视化"""
        if self.vis_config is None:
            return False
        
        frequency = getattr(self.vis_config, 'frequency', 1)
        return global_step % frequency == 0
```

#### 2. 扩展配置文件
在现有的 `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` 中添加可视化配置项：

```yaml
# 在现有配置文件末尾添加以下内容

# 可视化配置
visualization:
  enabled: true                           # 是否启用可视化功能
  sample_images_dir: "assets/test_images" # 测试图片目录
  max_samples: 10                         # 最大可视化样本数
  output_dir: null                        # 输出目录（null表示使用默认）
  frequency: 1                            # 可视化频率（每N步执行一次）
  debugger_theme: "white"                 # 调试器主题
  
  # LORE-TSR特有配置
  lore_tsr:
    show_logic_coordinates: true          # 是否显示逻辑坐标
    show_corner_arrows: true              # 是否显示角点箭头
    show_processor_output: true           # 是否显示Processor输出
    use_4ps_bbox: true                    # 是否使用四点边界框
  
  # 样式配置
  style:
    bbox_color: [0, 255, 0]               # 边界框颜色（绿色）
    logic_text_color: [255, 255, 0]       # 逻辑坐标文本颜色（黄色）
    corner_arrow_colors:                  # 角点箭头颜色[右,下,左,上]
      - [0, 0, 255]                       # 右箭头 - 红色
      - [0, 255, 0]                       # 下箭头 - 绿色  
      - [255, 0, 0]                       # 左箭头 - 蓝色
      - [0, 0, 0]                         # 上箭头 - 黑色
    transparency: 0.8                     # 透明度
    line_thickness: 2                     # 线条粗细
    arrow_thickness: 1                    # 箭头粗细
    text_size: 0.3                        # 文本大小
  
  # 热力图配置
  heatmap:
    colormap: "jet"                       # 颜色映射
    normalize: true                       # 是否归一化
    threshold: 0.1                        # 显示阈值
```

### 受影响的现有模块
- **配置系统**: 扩展OmegaConf配置结构，添加可视化相关配置项
- **可视化模块**: 在现有可视化目录中添加新的LORE-TSR专用组件
- **训练循环**: 后续步骤将集成到现有的train_lore_tsr.py中

### 复用已有代码
- **参考现有可视化器**: 借鉴 `table_structure_visualizer.py` 和 `table_structure_visualizer_ms.py` 的设计模式
- **复用配置系统**: 使用现有的OmegaConf配置解析机制
- **复用基础设施**: 利用现有的日志系统和设备管理

### 如何验证

#### 验证命令1 - 配置文件解析测试
```shell
python -c "
import sys; sys.path.append('.');
from omegaconf import OmegaConf;
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml');
print('✅ 配置文件解析成功');

# 检查可视化配置
has_vis_config = hasattr(config, 'visualization');
print(f'  - 可视化配置存在: {has_vis_config}');

if has_vis_config:
    print(f'  - 可视化启用状态: {config.visualization.enabled}');
    print(f'  - LORE-TSR特有配置: {hasattr(config.visualization, \"lore_tsr\")}');
    print(f'  - 样式配置: {hasattr(config.visualization, \"style\")}');

print('🎉 步骤9.1配置验证通过')
"
```

#### 验证命令2 - 可视化器导入测试
```shell
python -c "
import sys; sys.path.append('.');
from modules.visualization.lore_tsr_visualizer import LoreTsrVisualizer;
print('✅ LORE-TSR可视化器导入成功');

# 测试基础实例化
import torch;
from omegaconf import OmegaConf;
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml');
device = torch.device('cpu');
weight_dtype = torch.float32;

visualizer = LoreTsrVisualizer(config, device, weight_dtype);
print('  - 可视化器实例化成功');
print(f'  - 可视化启用检查: {visualizer._check_visualization_enabled()}');

print('🎉 步骤9.1可视化器验证通过')
"
```

#### 验证命令3 - 项目可运行性测试
```shell
python -c "
import sys; sys.path.append('.');
# 测试训练循环是否仍然可以正常导入
import importlib.util;
spec = importlib.util.spec_from_file_location(
    'train_lore_tsr', 
    'training_loops/table_structure_recognition/train_lore_tsr.py'
);
print('✅ 训练循环模块可正常加载');

# 测试配置系统完整性
from omegaconf import OmegaConf;
config = OmegaConf.load('configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml');
print('  - 配置系统正常工作');
print('  - 项目保持可运行状态');

print('🎉 步骤9.1项目可运行性验证通过')
"
```

## 🔄 当前迭代逻辑图

```mermaid
graph TD
    %% 当前迭代：迭代9步骤9.1 - 可视化基础框架和配置扩展

    subgraph "Source: LORE-TSR/lib/utils/debugger.py"
        direction LR
        debug_vis["可视化调试功能"]
        debug_4ps["四点边界框绘制"]
        debug_corner["角点箭头绘制"]
        debug_logic["逻辑坐标显示"]
    end

    subgraph "Source: LORE-TSR配置系统"
        direction LR
        opts_debug["调试相关参数"]
        opts_vis["可视化相关设置"]
    end

    subgraph "Target: train-anything可视化系统"
        direction TB
        T1["modules/visualization/lore_tsr_visualizer.py<br/>基础框架"]
        T2["configs/.../lore_tsr_config.yaml<br/>扩展配置"]
        T3["现有可视化基础设施<br/>复用"]
    end

    subgraph "现有参考系统"
        direction LR
        REF1["table_structure_visualizer.py"]
        REF2["image_utils.py"]
        REF3["OmegaConf配置系统"]
    end

    %% 迁移映射（步骤9.1）
    debug_vis -- "重构适配<br/>基础框架" --> T1
    opts_vis -- "重构适配<br/>YAML格式" --> T2
    
    %% 复用关系
    REF1 -.-> T1
    REF2 -.-> T1
    REF3 -.-> T2

    %% 依赖关系
    T2 -.-> T1
    T3 -.-> T1

    %% 后续步骤（灰色表示未实现）
    debug_4ps -.- T4["lore_tsr_image_utils.py<br/>（步骤9.2）"]
    debug_corner -.- T4
    debug_logic -.- T5["visualization_utils.py<br/>（步骤9.3）"]

    style T4 fill:#f9f9f9,stroke:#ccc,stroke-dasharray: 5 5
    style T5 fill:#f9f9f9,stroke:#ccc,stroke-dasharray: 5 5
```

## 📝 步骤完成标准

### 功能验收标准
1. ✅ **配置扩展成功**: 新的可视化配置项能被OmegaConf正确解析
2. ✅ **基础框架就绪**: LoreTsrVisualizer类能够正确实例化和基础功能检查
3. ✅ **项目可运行性**: 现有训练循环和功能保持完全正常
4. ✅ **向后兼容性**: 不影响train-anything现有的任何功能

### 技术验收标准
1. ✅ **代码质量**: 遵循train-anything的代码规范和设计模式
2. ✅ **模块化设计**: 可视化器作为独立模块，便于后续扩展
3. ✅ **配置驱动**: 所有可视化行为通过配置文件控制
4. ✅ **日志完整**: 提供详细的日志记录和错误处理

### 为下一步准备
- **步骤9.2**: 实现LORE-TSR图像处理工具（lore_tsr_image_utils.py）
- **步骤9.3**: 实现可视化辅助工具（visualization_utils.py）
- **步骤9.4**: 集成到训练循环并实现完整的可视化流程

---

**计划制定完成时间**: 2025-07-20  
**预估执行时间**: 0.5-1个工作日  
**风险评估**: 低（主要是基础框架搭建）  
**依赖关系**: 依赖迭代1-8的完成状态（已满足）
