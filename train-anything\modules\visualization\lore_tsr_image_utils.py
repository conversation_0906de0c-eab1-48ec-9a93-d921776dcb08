#!/usr/bin/env python3
"""
LORE-TSR专用图像处理工具
实现LORE-TSR特有的可视化功能，包括四点边界框绘制、逻辑坐标标注、角点箭头绘制等

Time: 2025-07-20
Author: Migration from LORE-TSR to train-anything
Description: 复制保留LORE-TSR原始的绘制逻辑，确保视觉效果一致性
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple, Any, Optional
import logging

logger = logging.getLogger(__name__)


class LoreTsrImageUtils:
    """
    LORE-TSR专用图像处理工具类
    
    提供LORE-TSR特有的可视化功能，包括：
    - 四点边界框绘制（基于add_4ps_coco_bbox）
    - 角点箭头绘制（基于add_corner）
    - 逻辑坐标标注
    - 图像预处理和后处理
    """
    
    @staticmethod
    def load_and_preprocess_image(image_path: str) -> <PERSON><PERSON>[Image.Image, np.ndarray, Dict[str, Any]]:
        """
        加载并预处理图像
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            Tuple[Image.Image, np.ndarray, Dict]: (原始图像, 预处理张量, 元信息)
        """
        try:
            # 加载原始图像
            original_image = Image.open(image_path).convert('RGB')
            
            # 转换为numpy数组用于预处理
            image_array = np.array(original_image)
            
            # 创建元信息字典
            meta = {
                'original_size': original_image.size,
                'image_path': image_path,
                'channels': len(original_image.getbands())
            }
            
            logger.debug(f"成功加载图像: {image_path}, 尺寸: {original_image.size}")
            return original_image, image_array, meta
            
        except Exception as e:
            logger.error(f"加载图像失败: {image_path}, 错误: {e}")
            raise
    
    @staticmethod
    def draw_lore_tsr_predictions(
        image: Image.Image, 
        predictions: Dict[str, Any], 
        style_config: Dict[str, Any]
    ) -> Image.Image:
        """
        在图像上绘制LORE-TSR预测结果
        
        Args:
            image: 目标图像
            predictions: 预测结果字典
            style_config: 样式配置
            
        Returns:
            Image.Image: 绘制后的图像
        """
        # 创建图像副本
        result_image = image.copy()
        
        # 转换为OpenCV格式进行绘制
        cv_image = cv2.cvtColor(np.array(result_image), cv2.COLOR_RGB2BGR)
        
        # 绘制边界框和逻辑坐标
        if 'bboxes' in predictions and 'logic_coords' in predictions:
            bboxes = predictions['bboxes']
            logic_coords = predictions['logic_coords']
            
            for i, (bbox, logic) in enumerate(zip(bboxes, logic_coords)):
                cv_image = LoreTsrImageUtils.draw_4ps_bbox_with_logic(
                    cv_image, bbox, logic, style_config
                )
        
        # 绘制角点箭头
        if 'corners' in predictions:
            corners = predictions['corners']
            for corner in corners:
                cv_image = LoreTsrImageUtils.draw_corner_arrows(
                    cv_image, corner, style_config.get('corner_arrow_colors', []), 
                    style_config.get('arrow_thickness', 1)
                )
        
        # 转换回PIL格式
        result_image = Image.fromarray(cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB))
        return result_image
    
    @staticmethod
    def draw_4ps_bbox_with_logic(
        image: np.ndarray, 
        bbox: List[float], 
        logic_coords: List[int], 
        style: Dict[str, Any]
    ) -> np.ndarray:
        """
        绘制四点边界框并标注逻辑坐标信息
        基于LORE-TSR原始的add_4ps_coco_bbox函数实现
        
        Args:
            image: 目标图像（OpenCV格式）
            bbox: 四点边界框坐标 [x1,y1,x2,y2,x3,y3,x4,y4]
            logic_coords: 逻辑坐标 [start_row, end_row, start_col, end_col]
            style: 样式配置
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        # 转换坐标为整数 - 复制LORE-TSR原始逻辑
        bbox = np.array(bbox, dtype=np.int32)
        
        # 获取样式配置
        bbox_color = tuple(style.get('bbox_color', [0, 255, 0]))  # 默认绿色
        line_thickness = style.get('line_thickness', 2)
        text_color = tuple(style.get('logic_text_color', [255, 255, 0]))  # 默认黄色
        text_size = style.get('text_size', 0.3)
        
        # 绘制四点边界框 - 复制LORE-TSR原始绘制逻辑
        # 将8个坐标重新组织为4个点
        points = bbox.reshape(4, 2)
        
        # 使用polylines绘制四边形 - 保持与LORE-TSR一致的绘制方式
        cv2.polylines(image, [points], True, bbox_color, line_thickness)
        
        # 格式化并绘制逻辑坐标文本 - 复制LORE-TSR原始逻辑
        if logic_coords is not None and len(logic_coords) >= 4:
            # 格式化逻辑坐标文本 - 保持LORE-TSR原始格式
            logic_text = f"({logic_coords[0]},{logic_coords[1]},{logic_coords[2]},{logic_coords[3]})"
            
            # 计算文本位置（在边界框左上角） - 复制LORE-TSR原始位置计算
            text_position = (bbox[0], bbox[1] - 5)
            
            # 获取文本尺寸 - 复制LORE-TSR原始字体设置
            font = cv2.FONT_HERSHEY_SIMPLEX
            (text_width, text_height), baseline = cv2.getTextSize(
                logic_text, font, text_size, 1
            )
            
            # 绘制文本背景矩形 - 复制LORE-TSR原始背景绘制
            cv2.rectangle(
                image,
                (text_position[0], text_position[1] - text_height - baseline),
                (text_position[0] + text_width, text_position[1] + baseline),
                bbox_color, -1
            )
            
            # 绘制逻辑坐标文本 - 复制LORE-TSR原始文本绘制
            cv2.putText(
                image, logic_text, text_position, font, text_size, 
                (0, 0, 0), thickness=1, lineType=cv2.LINE_AA
            )
        
        return image
    
    @staticmethod
    def draw_corner_arrows(
        image: np.ndarray, 
        corner: List[float], 
        colors: List[List[int]], 
        thickness: int
    ) -> np.ndarray:
        """
        绘制角点方向箭头，显示表格结构的方向信息
        基于LORE-TSR原始的add_corner函数实现
        
        Args:
            image: 目标图像（OpenCV格式）
            corner: 角点信息 [x,y,dx1,dy1,dx2,dy2,dx3,dy3,dx4,dy4]
            colors: 四个方向的箭头颜色 [[r,g,b], ...]
            thickness: 箭头线条粗细
            
        Returns:
            np.ndarray: 绘制后的图像
        """
        if len(corner) < 10:
            logger.warning(f"角点数据不完整，需要10个值，实际得到{len(corner)}个")
            return image
        
        # 中心点坐标 - 复制LORE-TSR原始坐标提取
        center_x, center_y = int(corner[0]), int(corner[1])
        
        # 绘制中心点
        cv2.circle(image, (center_x, center_y), 3, (255, 0, 0), -1)
        
        # 默认颜色（如果colors不足） - 复制LORE-TSR原始颜色设置
        default_colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 0, 0)]
        
        # 绘制四个方向的箭头 - 完全复制LORE-TSR原始逻辑
        # 复制LORE-TSR原始的四个方向检查和绘制逻辑
        
        # 右方向箭头 - 复制原始逻辑：corner[2], corner[3]
        if abs(int(corner[2]) + int(corner[3])) > 10:
            end_x = int(corner[0] + corner[2])
            end_y = int(corner[1] + corner[3])
            color = tuple(colors[0][:3]) if len(colors) > 0 and len(colors[0]) >= 3 else default_colors[0]
            cv2.arrowedLine(
                image, 
                (center_x, center_y), 
                (end_x, end_y), 
                color, 
                thickness=thickness, 
                line_type=cv2.LINE_4, 
                shift=0, 
                tipLength=0.02
            )
        
        # 下方向箭头 - 复制原始逻辑：corner[4], corner[5]
        if abs(int(corner[4]) + int(corner[5])) > 10:
            end_x = int(corner[0] + corner[4])
            end_y = int(corner[1] + corner[5])
            color = tuple(colors[1][:3]) if len(colors) > 1 and len(colors[1]) >= 3 else default_colors[1]
            cv2.arrowedLine(
                image, 
                (center_x, center_y), 
                (end_x, end_y), 
                color, 
                thickness=thickness, 
                line_type=cv2.LINE_4, 
                shift=0, 
                tipLength=0.02
            )
        
        # 左方向箭头 - 复制原始逻辑：corner[6], corner[7]
        if abs(int(corner[6]) + int(corner[7])) > 10:
            end_x = int(corner[0] + corner[6])
            end_y = int(corner[1] + corner[7])
            color = tuple(colors[2][:3]) if len(colors) > 2 and len(colors[2]) >= 3 else default_colors[2]
            cv2.arrowedLine(
                image, 
                (center_x, center_y), 
                (end_x, end_y), 
                color, 
                thickness=thickness, 
                line_type=cv2.LINE_4, 
                shift=0, 
                tipLength=0.02
            )
        
        # 上方向箭头 - 复制原始逻辑：corner[8], corner[9]
        if abs(int(corner[8]) + int(corner[9])) > 10:
            end_x = int(corner[0] + corner[8])
            end_y = int(corner[1] + corner[9])
            color = tuple(colors[3][:3]) if len(colors) > 3 and len(colors[3]) >= 3 else default_colors[3]
            cv2.arrowedLine(
                image, 
                (center_x, center_y), 
                (end_x, end_y), 
                color, 
                thickness=thickness, 
                line_type=cv2.LINE_4, 
                shift=0, 
                tipLength=0.02
            )
        
        return image

    @staticmethod
    def create_logic_axis_visualization(
        logic_axis: np.ndarray,
        image_size: Tuple[int, int]
    ) -> Image.Image:
        """
        创建Processor输出的逻辑轴向可视化

        Args:
            logic_axis: 逻辑轴向张量 [batch_size, seq_len, 4] 或 [seq_len, 4]
            image_size: 目标图像尺寸 (width, height)

        Returns:
            Image.Image: 逻辑轴向可视化图像
        """
        width, height = image_size

        # 创建空白画布
        canvas = np.ones((height, width, 3), dtype=np.uint8) * 255

        # 处理张量维度
        if len(logic_axis.shape) == 3:
            logic_axis = logic_axis[0]  # 取第一个batch

        # 如果是torch张量，转换为numpy
        if hasattr(logic_axis, 'detach'):
            logic_axis = logic_axis.detach().cpu().numpy()

        # 绘制网格和逻辑坐标
        for i, coords in enumerate(logic_axis):
            if len(coords) >= 4:
                start_row, end_row, start_col, end_col = coords[:4]

                # 计算网格位置（简化版本）
                cell_width = width // 20  # 假设最大20列
                cell_height = height // 20  # 假设最大20行

                x1 = int(start_col * cell_width)
                y1 = int(start_row * cell_height)
                x2 = int((end_col + 1) * cell_width)
                y2 = int((end_row + 1) * cell_height)

                # 确保坐标在画布范围内
                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(width, x2), min(height, y2)

                # 绘制网格单元
                color = (100 + (i * 30) % 155, 100 + (i * 50) % 155, 100 + (i * 70) % 155)
                cv2.rectangle(canvas, (x1, y1), (x2, y2), color, 2)

                # 标注行列信息
                text = f"({int(start_row)},{int(end_row)},{int(start_col)},{int(end_col)})"
                cv2.putText(canvas, text, (x1 + 5, y1 + 15),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 0), 1)

        # 转换为PIL图像
        return Image.fromarray(cv2.cvtColor(canvas, cv2.COLOR_BGR2RGB))

    @staticmethod
    def format_logic_coordinates(logic_coords: List[int]) -> str:
        """
        格式化逻辑坐标为显示文本

        Args:
            logic_coords: 逻辑坐标 [start_row, end_row, start_col, end_col]

        Returns:
            str: 格式化的文本
        """
        if len(logic_coords) >= 4:
            return f"({logic_coords[0]},{logic_coords[1]},{logic_coords[2]},{logic_coords[3]})"
        else:
            return f"({','.join(map(str, logic_coords))})"
