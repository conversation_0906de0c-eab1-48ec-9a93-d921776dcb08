<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cd9e17ef-ec20-412b-ab37-d6865fd85370" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/cocoapi" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/cocoapi" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ziLYuOl8cQREWN9X32RFXr1sZl" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.train_single.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.mappings": "true",
    "WebServerToolWindowPanel.toolwindow.highlight.symlinks": "true",
    "WebServerToolWindowPanel.toolwindow.show.date": "false",
    "WebServerToolWindowPanel.toolwindow.show.permissions": "false",
    "WebServerToolWindowPanel.toolwindow.show.size": "false",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "D:/workspace/projects/TSRTransplantation/LORE-TSR/vibecoding-adapt_modern_datasets_to_LORE",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\vibecoding-adapt_modern_datasets_to_LORE" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\scripts\train" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\scripts\infer" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\bak" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\lib\models\networks\bak\DCNv_latest" />
      <recent name="D:\workspace\projects\TSRTransplantation\LORE-TSR\src\lib\models\networks" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="train_single" type="PythonConfigurationType" factoryName="Python">
      <module name="LORE-TSR" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Remote Python 3.10.13 (sftp://root@************:1322/opt/miniforge3/envs/lore/bin/python3)" />
      <option name="WORKING_DIRECTORY" value="D:\workspace\projects\TSRTransplantation\LORE-TSR\src" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/main.py" />
      <option name="PARAMETERS" value="ctdet --dataset_name table --exp_id training_wtw --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/train --wiz_4ps --wiz_stacking --wiz_pairloss --tsfm_layers 3 --stacking_layers 3 --batch_size 2 --master_batch 6 --arch dla_34 --lr 1e-4 --K 500 --MK 1000 --num_epochs 100 --lr_step '70, 90' --gpus 0 --num_workers 16 --val_intervals 10" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cd9e17ef-ec20-412b-ab37-d6865fd85370" name="Changes" comment="" />
      <created>1752208998120</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752208998120</updated>
      <workItem from="1752208999237" duration="60000" />
      <workItem from="1752209088998" duration="1159000" />
      <workItem from="1752210286129" duration="17755000" />
      <workItem from="1752455455994" duration="6224000" />
      <workItem from="1752462214569" duration="8496000" />
      <workItem from="1752550697505" duration="12645000" />
      <workItem from="1752589119682" duration="178000" />
      <workItem from="1752592015609" duration="15116000" />
      <workItem from="1752809820235" duration="621000" />
      <workItem from="1752945273221" duration="1505000" />
      <workItem from="1752971451534" duration="2849000" />
      <workItem from="1752982398241" duration="1655000" />
      <workItem from="1753022997361" duration="678000" />
      <workItem from="1753026402236" duration="13803000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/opts.py</url>
          <line>81</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/opts.py</url>
          <line>358</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/lib/datasets/dataset/table.py</url>
          <line>26</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/src/demo.py</url>
          <line>26</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/LORE_TSR$train_single.coverage" NAME="train_single Coverage Results" MODIFIED="1752234376515" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:\workspace\projects\TSRTransplantation\LORE-TSR\src" />
  </component>
</project>